import { createTheme as createThemeMui, extendTheme } from '@mui/material/styles';

import { colorSchemes, darkTheme } from './color-schemes';
import { components } from './components/components';
import { shadows } from './shadows';
import type { Theme } from './types';
import { typography } from './typography';

declare module '@mui/material/styles/createPalette' {
  interface PaletteRange {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  }

  interface Palette {
    neutral: PaletteRange;
  }

  interface PaletteOptions {
    neutral?: PaletteRange;
  }

  interface TypeBackground {
    level1: string;
    level2: string;
    level3: string;
  }
}

export function createDarkTheme() {
  return createThemeMui({
    breakpoints: { values: { xs: 0, sm: 600, md: 900, lg: 1200, xl: 1440 } },
    palette: {
      mode: 'dark',
    },
    components,
    colorSchemes: {
      dark: darkTheme,
      light: darkTheme,
    },
    shadows,
    shape: { borderRadius: 12 }, // Updated to match design system
    typography,
  });
}

export function createTheme(): Theme {
  return extendTheme({
    breakpoints: { values: { xs: 0, sm: 600, md: 900, lg: 1200, xl: 1440 } },
    components,
    colorSchemes,
    shadows,
    shape: { borderRadius: 12 }, // Updated to match design system
    typography,
    colorSchemeSelector: 'class',
  });
}
